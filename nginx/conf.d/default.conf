# 上游服务器配置（NLWEB API已移除）

upstream ldap_backend {
    server app:3002;
    keepalive 8;
}

server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # 主应用路由
    location / {
        try_files $uri $uri/ /index.html;
        
        # 静态文件缓存 - 区分JS/CSS和其他资源
        location ~* \.(js|css)$ {
            expires 1h;
            add_header Cache-Control "public, max-age=3600";
            access_log off;
        }
        
        location ~* \.(png|jpg|jpeg|gif|ico|svg|woff|woff2)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
        }
    }

    # API代理（NLWEB API已移除，如需其他API请重新配置）

    # LDAP认证API代理
    location /api/ldap/ {
        proxy_pass http://ldap_backend/api/ldap/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 5s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # 兼容旧的LDAP代理路径
    location /ldap/ {
        proxy_pass http://ldap_backend/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 5s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # 注意：LiteLLM API不通过Nginx代理，前端直接访问*************:14000

    # 健康检查（使用LDAP服务）
    location /health {
        proxy_pass http://ldap_backend/health;
        access_log off;
    }

    # 错误页面
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
} 