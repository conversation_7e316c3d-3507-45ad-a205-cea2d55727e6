import { useEffect } from 'react';

const ResourcePreloader = () => {
  useEffect(() => {
    // 预加载关键资源
    const preloadResources = [
      // 预加载关键字体
      'assets/fonts/optimized-fonts.css',
      // 预加载关键图片
      'favicon.svg',
    ];

    preloadResources.forEach(resource => {
      const link = document.createElement('link');
      link.rel = 'preload';
      
      if (resource.endsWith('.css')) {
        link.as = 'style';
        link.href = resource;
      } else if (resource.endsWith('.js')) {
        link.as = 'script';
        link.href = resource;
      } else {
        link.as = 'image';
        link.href = resource;
      }
      
      document.head.appendChild(link);
    });

    // 预连接到外部域名
    const preconnectDomains = [
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com'
    ];

    preconnectDomains.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'preconnect';
      link.href = domain;
      document.head.appendChild(link);
    });

    // 预加载关键 CSS
    const criticalCSS = `
      .critical-above-fold {
        will-change: transform;
        transform: translateZ(0);
      }
      .optimize-paint {
        contain: layout style paint;
      }
    `;

    const style = document.createElement('style');
    style.textContent = criticalCSS;
    document.head.appendChild(style);

    // 清理函数
    return () => {
      try {
        // 安全清理预加载的资源
        const preloadLinks = document.querySelectorAll('link[rel="preload"]');
        preloadLinks.forEach(link => {
          try {
            if (preloadResources.some(resource => link.href.includes(resource))) {
              if (link.parentNode && link.parentNode.contains(link)) {
                link.remove();
              }
            }
          } catch (error) {
            console.warn('清理预加载资源失败:', error.message);
          }
        });
      } catch (error) {
        console.warn('清理预加载资源时出错:', error.message);
      }
    };
  }, []);

  return null; // 这是一个无渲染组件
};

export default ResourcePreloader; 