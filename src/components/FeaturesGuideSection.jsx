import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { 
  FaRocket, FaCode, FaLightbulb, FaShieldAlt, FaUsers, FaChartLine,
  FaKeyboard, FaBrain, FaTools, FaCloudDownloadAlt, FaComments, FaCog, FaSpinner
} from 'react-icons/fa';
import metricsService from '../services/metricsService';
import { getLiteLLMApiBase } from '../config/apiConfig.js';

const FeaturesGuideSection = React.memo(() => {
  const [metrics, setMetrics] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [error, setError] = useState(null);

  // 优化的获取指标函数 - 使用useCallback防止重复创建
  const fetchMetrics = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await metricsService.getPlatformMetrics();
      setMetrics(data);
      setLastUpdated(new Date());
    } catch (error) {
      console.warn('获取指标失败，使用模拟数据:', error.message);
      setError(error.message);
      // 使用模拟数据作为降级
      setMetrics({
        totalModels: 3,
        totalRequests: 1.2,
        totalUsers: 15,
        uptime: '99.5%',
        dataSource: 'fallback'
      });
      setLastUpdated(new Date());
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 优化useEffect - 减少API调用频率，添加清理机制
  useEffect(() => {
    fetchMetrics();
    
    // 设置定时刷新（每10分钟而不是5分钟，减少服务器压力）
    const interval = setInterval(fetchMetrics, 10 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, [fetchMetrics]);

  // 使用useMemo缓存静态数据，避免重复计算
  const stats = useMemo(() => {
    if (!metrics) {
      return [
        { number: <FaSpinner className="animate-spin" />, label: '支持AI模型', color: 'text-cyan-400', isLoading: true },
        { number: <FaSpinner className="animate-spin" />, label: 'API调用量 (千次)', color: 'text-blue-400', isLoading: true },
        { number: <FaSpinner className="animate-spin" />, label: '活跃用户', color: 'text-purple-400', isLoading: true },
        { number: <FaSpinner className="animate-spin" />, label: '服务可用性', color: 'text-green-400', isLoading: true },
      ];
    }

    return [
      { number: metrics.totalModels || 3, label: '支持AI模型', color: 'text-cyan-400', isLoading: false },
      { number: `${metrics.totalRequests || 1.2}K`, label: 'API调用量 (千次)', color: 'text-blue-400', isLoading: false },
      { number: metrics.totalUsers || 15, label: '活跃用户', color: 'text-purple-400', isLoading: false },
      { number: metrics.uptime || '99.5%', label: '服务可用性', color: 'text-green-400', isLoading: false },
    ];
  }, [metrics]);

  // 缓存核心功能数据
  const coreFeatures = useMemo(() => [
    {
      icon: <FaRocket className="text-cyan-400 text-4xl" />,
      title: 'AI智能对话',
      subtitle: '下一代AI交互体验',
      color: 'from-cyan-400 to-blue-500',
      description: '基于先进的大语言模型，提供自然流畅的AI对话体验。支持代码生成、问题解答、创意写作等多种场景，让AI成为您的智能助手。',
      benefits: [
        '✨ 支持多种AI模型',
                    '实时响应速度',
                    '上下文理解',
                  '创意内容生成'
      ],
      demoText: '请帮我生成一个React组件'
    },
    {
      icon: <FaShieldAlt className="text-blue-400 text-4xl" />,
      title: 'API密钥管理',
      subtitle: '安全便捷的密钥服务',
      color: 'from-blue-400 to-purple-500',
      description: '企业级API密钥管理系统，提供安全的密钥存储、权限控制和使用监控。支持多平台密钥统一管理，让AI服务接入更简单。',
      benefits: [
                  '企业级安全',
          '使用量监控',
          '快速配置',
          '自动轮换'
      ],
      demoText: 'sk-proj-...'
    },
    {
      icon: <FaTools className="text-purple-400 text-4xl" />,
      title: '开发工具集成',
      subtitle: 'IDE插件与开发者工具',
      color: 'from-purple-400 to-pink-500',
      description: '完整的开发工具生态系统，包括VS Code、JetBrains等主流IDE插件。提供代码补全、智能重构、文档生成等功能，提升开发效率。',
      benefits: [
                  'IDE原生集成',
          '代码智能补全',
          '自动文档生成',
          '项目模板生成'
      ],
      demoText: 'cline-3.17.12.vsix'
    }
  ], []);

  // 缓存使用场景数据
  const useCases = useMemo(() => [
    {
              icon: <i className="fas fa-laptop-code text-cyan-400"></i>,
      title: '软件开发',
      scenarios: [
        '代码生成与调试',
        '技术文档编写',
        '架构设计咨询',
        '代码审查优化'
      ]
    },
    {
              icon: <i className="fas fa-chart-bar text-blue-400"></i>,
      title: '数据分析',
      scenarios: [
        'SQL查询生成',
        '数据可视化建议',
        '报表模板创建',
        '业务洞察分析'
      ]
    },
    {
              icon: <i className="fas fa-graduation-cap text-green-400"></i>,
      title: '学习培训',
      scenarios: [
        '编程教学辅助',
        '技术答疑解惑',
        '最佳实践指导',
        '项目实战演练'
      ]
    },
    {
              icon: '🚀',
      title: '创新研发',
      scenarios: [
        '原型快速开发',
        '算法优化建议',
        '技术调研支持',
        '创意方案生成'
      ]
    }
  ], []);

  // 手动刷新数据
  const handleRefresh = useCallback(() => {
    if (!isLoading) {
      fetchMetrics();
    }
  }, [isLoading, fetchMetrics]);

  return (
    <section id="features" className="relative py-20 bg-black overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-cyan-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-0 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 数据统计 */}
        <div className="text-center mb-20 animate-fade-in-up">
          <div className="flex items-center justify-center gap-4 mb-8">
            <h3 className="text-3xl font-bold text-white"><i className="fas fa-chart-bar text-cyan-400 mr-2"></i>实时平台数据</h3>
            {isLoading ? (
              <FaSpinner className="text-cyan-400 animate-spin" />
            ) : null}
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="text-cyan-400 hover:text-cyan-300 disabled:opacity-50 text-sm underline"
              title="手动刷新数据"
            >
              刷新
            </button>
          </div>
          
          {/* 数据来源说明 */}
          <div className="mb-8 text-sm text-gray-500">
            {error ? (
              <p className="text-red-400">⚠️ 数据加载失败: {error}</p>
            ) : metrics?.dataSource === 'api' ? (
              <>
                <p className="text-green-400">✅ 数据来源: LLM API ({getLiteLLMApiBase()})</p>
                {lastUpdated ? (
                  <p>最后更新: {lastUpdated.toLocaleTimeString('zh-CN')}</p>
                ) : null}
              </>
            ) : (
              <>
                <p className="text-yellow-400">⚠️ 数据来源: 模拟数据 (LLM API暂不可用)</p>
                <p className="text-xs">请检查LLM服务状态或API密钥配置</p>
              </>
            )}
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div
                key={index}
                className="text-center bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-xl p-6 hover:border-cyan-500/50 transition-all duration-300 animate-scale-in"
                style={{ animationDelay: `${index * 0.1}s`, animationFillMode: 'both' }}
              >
                <div className={`text-4xl md:text-5xl font-bold ${stat.color} mb-2 flex items-center justify-center gap-2`}>
                  {stat.isLoading ? (
                    <FaSpinner className="animate-spin text-2xl" />
                  ) : (
                    stat.number
                  )}
                </div>
                <div className="text-gray-400 text-sm">{stat.label}</div>
              </div>
            ))}
          </div>
          
          {/* 数据说明 */}
          <div className="mt-8 text-xs text-gray-500 max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
              <div>
                <h4 className="text-cyan-400 font-semibold mb-2">📈 数据说明:</h4>
                <ul className="space-y-1">
                  <li>• <strong>支持AI模型:</strong> 从 /models 接口获取的可用模型数量</li>
                  <li>• <strong>API调用量:</strong> 近30天的token消耗统计（千计）</li>
                </ul>
              </div>
              <div>
                <ul className="space-y-1 mt-6 md:mt-0">
                  <li>• <strong>活跃用户:</strong> 基于API密钥的唯一用户数</li>
                  <li>• <strong>服务可用性:</strong> LLM服务健康状态</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* 标题区域 */}
        <div className="text-center mb-20 animate-fade-in-up" style={{ animationDelay: '0.5s', animationFillMode: 'both' }}>
          <h2 className="text-5xl md:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
              核心功能特性
            </span>
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
            AI智能对话 + API密钥管理 + 开发工具文档，企业级AI开发平台
          </p>
        </div>

        {/* 核心功能详细介绍 */}
        <div className="space-y-20 mb-20">
          {coreFeatures.map((feature, index) => (
            <div
              key={index}
              className={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} items-center gap-12 animate-fade-in-up`}
              style={{ animationDelay: `${(index + 1) * 0.2}s`, animationFillMode: 'both' }}
            >
              {/* 功能内容 */}
              <div className="flex-1 space-y-6">
                <div className="flex items-center gap-4">
                  {feature.icon}
                  <div>
                    <h3 className="text-3xl font-bold text-white">{feature.title}</h3>
                    <p className={`text-lg bg-gradient-to-r ${feature.color} bg-clip-text text-transparent font-semibold`}>
                      {feature.subtitle}
                    </p>
                  </div>
                </div>
                
                <p className="text-xl text-gray-300 leading-relaxed">
                  {feature.description}
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {feature.benefits.map((benefit, i) => (
                    <div key={i} className="flex items-center text-gray-400">
                      <span className="mr-2">{benefit}</span>
                    </div>
                  ))}
                </div>
                
                <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-lg p-4">
                  <p className="text-sm text-gray-500 mb-2">示例</p>
                  <p className="text-cyan-400 font-mono">{feature.demoText}</p>
                </div>
              </div>

              {/* 功能演示区域 */}
              <div className="flex-1">
                <div className="bg-gray-900/80 backdrop-blur-sm border border-gray-700 rounded-xl p-8 shadow-2xl">
                  <div className="flex items-center justify-between mb-6">
                    <h4 className="text-lg font-semibold text-white">功能演示</h4>
                    <div className="flex gap-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="bg-gray-800 rounded-lg p-4">
                      <p className="text-gray-400 text-sm mb-2">用户输入:</p>
                      <p className="text-white font-mono text-sm">{feature.demoText}</p>
                    </div>
                    
                    <div className="bg-gradient-to-r from-cyan-500/10 to-blue-500/10 border border-cyan-500/20 rounded-lg p-4">
                      <p className="text-gray-400 text-sm mb-2">AI回复:</p>
                      <div className="flex items-center gap-2">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                        <span className="text-cyan-400 text-sm">正在生成智能回复...</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 使用场景 */}
        <div className="mb-20 animate-fade-in-up" style={{ animationDelay: '1s', animationFillMode: 'both' }}>
          <h3 className="text-4xl font-bold text-center mb-12 text-white">
            适用场景
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {useCases.map((useCase, index) => (
              <div
                key={index}
                className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl p-6 hover:border-cyan-500/50 transition-all duration-300 animate-scale-in"
                style={{ animationDelay: `${1.2 + index * 0.1}s`, animationFillMode: 'both' }}
              >
                <div className="text-cyan-400 text-3xl mb-4">{useCase.icon}</div>
                <h4 className="text-xl font-semibold text-white mb-4">{useCase.title}</h4>
                <ul className="space-y-2">
                  {useCase.scenarios.map((scenario, i) => (
                    <li key={i} className="text-gray-400 text-sm flex items-center">
                      <span className="text-cyan-400 mr-2">▸</span>
                      {scenario}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

      </div>
    </section>
  );
});

FeaturesGuideSection.displayName = 'FeaturesGuideSection';

export default FeaturesGuideSection; 