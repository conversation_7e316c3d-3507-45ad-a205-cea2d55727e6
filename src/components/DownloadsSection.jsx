import React, { useState } from 'react';
import { HiDownload, HiX } from 'react-icons/hi';
import { FaLaptopCode, FaCheckCircle, FaCode, FaRobot, FaCopy } from 'react-icons/fa';
import { VscCode } from 'react-icons/vsc';
import { SiIntellijidea } from 'react-icons/si';
import { getLiteLLMApiBase } from '../config/apiConfig.js';

const DownloadsSection = ({ user }) => {
  const [selectedTool, setSelectedTool] = useState(null);
  const [showModal, setShowModal] = useState(false);

  // 复制到剪贴板功能
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      alert('已复制到剪贴板');
    });
  };

  // 获取工具下载信息
  const getDownloadInfo = (tool) => {
    const apiBase = `${getLiteLLMApiBase()}`;
    
    switch (tool.id) {
      case 'vscode-ide':
        return {
          title: 'VS Code IDE 下载',
          downloadLinks: [
            {
              platform: 'Windows 10/11',
              url: '/downloads/tools/vscode-ide/windows/VSCode-win32-x64-1.101.0.zip',
              size: '~156MB',
              icon: '🪟',
              description: '适用于Windows 10/11的VS Code安装包'
            },
            {
              platform: 'Windows 7',
              url: '/downloads/tools/vscode-ide/windows/VSCode-win32-x64-1.81.1.zip',
              size: '~156MB',
              icon: '🪟',
              description: '适用于Windows 7的VS Code安装包'
            },
            {
              platform: 'plugins',
              url: '/downloads/tools/vscode-ide/windows/plugins.zip',
              size: '~191MB',
              icon: '🔌',
              description: 'VS Code常用插件合集'
            }
            // },
            // {
            //   platform: 'macOS',
            //   url: '/downloads/tools/vscode-ide/macos/VSCode-darwin-universal-latest.zip',
            //   size: '~120MB',
            //   icon: '🍎',
            //   description: '适用于macOS的VS Code安装包'
            // },
            // {
            //   platform: 'Linux',
            //   url: '/downloads/tools/vscode-ide/linux/code_latest_amd64.deb',
            //   size: '~75MB',
            //   icon: '🐧',
            //   description: '适用于Linux发行版的VS Code安装包'
            // }
          ],
          installSteps: [
            '下载适合您操作系统的安装包',
            '运行安装程序，按照向导完成安装',
            '安装完成后即可使用平台提供的插件'
          ],
          notes: [
            '如需帮助，请联系技术支持团队',
            '建议安装最新稳定版本以获得最佳兼容性'
          ]
        };
      case 'cline-plugin':
        return {
          title: 'Cline插件 下载',
          downloadLinks: [
            {
              platform: '最新版本',
              url: '/downloads/tools/cline-plugin/latest/cline-3.17.12.vsix',
              size: '~11MB',
              icon: '🚀',
              description: '最新功能和bug修复，推荐下载'
            }
          ],
          installSteps: [
            '下载 .vsix 插件文件',
            '打开VS Code，按 Ctrl+Shift+P 打开命令面板',
            '输入 "Extensions: Install from VSIX" 并选择',
            '选择下载的 .vsix 文件进行安装',
            '重启VS Code使插件生效'
          ],
          config: {
            apiUrl: apiBase,
            model: 'deepseek-reasoner',
            note: '使用您在平台生成的API密钥进行配置'
          },
          notes: [
            '安装后需要配置API连接才能正常使用',
            '确保VS Code版本在1.74.0以上'
          ]
        };
      case 'roocode-plugin':
        return {
          title: 'RooCode插件 下载',
          downloadLinks: [
            {
              platform: '最新版本',
              url: '/downloads/tools/roocode-plugin/latest/RooVeterinaryInc.roo-cline-3.20.3.vsix',
              size: '~3.2MB',
              icon: '🚀',
              description: '最新功能，支持四种AI模式'
            }
          ],
          installSteps: [
            '下载 .vsix 插件文件',
            '打开VS Code，按 Ctrl+Shift+P 打开命令面板',
            '输入 "Extensions: Install from VSIX" 并选择',
            '选择下载的 .vsix 文件进行安装',
            '重启VS Code使插件生效'
          ],
          config: {
            apiUrl: apiBase,
            model: 'deepseek-reasoner',
            note: '使用您在平台生成的API密钥进行配置'
          },
          notes: [
            '支持Code/Architect/Ask/Debug四种模式',
            '安装后需要配置API连接才能正常使用'
          ]
        };
      case 'jetbrains-ai-assistant':
        return {
          title: 'JetBrains AI Assistant 下载',
          downloadLinks: [
            {
              platform: '通用版本',
              url: '/downloads/tools/jetbrains-ai-assistant/latest/ml-llm-252.21735.40.zip',
              size: '~93MB',
              icon: '🚀',
              description: '适用于所有JetBrains IDE的通用版本'
            }
          ],
          installSteps: [
            '下载插件安装包文件',
            '打开JetBrains IDE（如IntelliJ IDEA、PyCharm等）',
            '进入 File → Settings → Plugins',
            '点击齿轮图标，选择 "Install Plugin from Disk"',
            '选择插件文件进行安装',
            '重启IDE使插件生效'
          ],
          config: {
            apiUrl: apiBase,
            model: 'deepseek-reasoner',
            note: '使用您在平台生成的API密钥进行配置'
          },
          notes: [
            '支持IntelliJ IDEA、PyCharm、WebStorm等JetBrains系列IDE',
            '安装后需要配置API连接才能正常使用'
          ]
        };
      default:
        return {
          title: '工具下载',
          downloadLinks: [],
          installSteps: ['该工具正在准备中，请联系系统管理员获取安装包'],
          notes: []
        };
    }
  };
  
  // 处理下载点击事件
  const handleDownload = (tool) => {
    if (!user) {
      alert('请先登录后再进行下载');
      return;
    }

    setSelectedTool(tool);
    setShowModal(true);
  };

  const aiTools = [
    {
      id: 'vscode-ide',
      name: 'VS Code IDE',
      icon: <VscCode className="text-5xl text-blue-500" />,
      description: 'VS Code IDE',
      version: 'v1.101.0',
      special: false
    },
    {
      id: 'cline-plugin',
      name: 'Cline插件',
      icon: <FaCode className="text-5xl text-cyan-400" />,
      description: 'AI编程助手插件，支持智能代码生成（离线安装）',
      version: 'v3.17.12',
      special: true
    },
    {
      id: 'roocode-plugin',
      name: 'RooCode插件',
      icon: <FaRobot className="text-5xl text-purple-400" />,
      description: '多模式AI助手，支持Code/Architect/Ask/Debug模式（离线安装）',
      version: 'v3.19',
      special: true
    },
    {
      id: 'jetbrains-ai-assistant',
      name: 'JetBrains AI Assistant',
      icon: <SiIntellijidea className="text-5xl text-red-500" />,
      description: 'JetBrains IDE系列AI助手插件（离线安装）',
      version: 'v252.19874.38',
      special: false
    }
  ];

  return (
    <section id="downloads" className="py-20 bg-gray-900 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-b from-black to-gray-900"></div>
        <div className="absolute top-1/2 left-0 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题 */}
        <div
          className="text-center mb-16"
        >
          <h2 className="text-5xl font-bold mb-4 bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">
            工具下载中心
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            下载最新的开发工具和AI助手，提升您的开发效率
          </p>
          {!user ? (
            <div
              className="mt-4 inline-flex items-center gap-2 px-4 py-2 bg-yellow-500/20 border border-yellow-500/50 rounded-lg text-yellow-400 text-sm"
            >
              <FaLaptopCode />
              <span>下载前需要先登录账户</span>
            </div>
          ) : (
            <div
              className="mt-4 inline-flex items-center gap-2 px-4 py-2 bg-green-500/20 border border-green-500/50 rounded-lg text-green-400 text-sm"
            >
              <FaCheckCircle />
              <span>欢迎 {user.name}，您可以下载所有工具</span>
            </div>
          )}
        </div>

        {/* AI工具下载 */}
        {/* <div className="mb-16">
          <h3
            className="text-2xl font-bold text-white mb-8"
          >
            AI 开发工具
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {aiTools.map((tool, _index) => (
              <div
                key={tool.id}
                className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl p-6"
              >
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h4 className="text-xl font-semibold text-white mb-2">{tool.name}</h4>
                    <p className="text-gray-400 text-sm mb-1">{tool.description}</p>
                    <p className="text-cyan-400 text-sm">{tool.version}</p>
                  </div>
                </div>
                <div className="flex flex-wrap gap-3">
                  {tool.platforms.map((platform, pIndex) => (
                    <button
                      key={pIndex}
                      onClick={(e) => handleDownload(e, platform.url, `${tool.name} ${platform.name}版本`)}}}
                      className="inline-flex items-center gap-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                    >
                      {platform.icon}
                      <span>{platform.name}</span>
                      <HiDownload />
                    </button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div> */}

        {/* AI开发工具下载 */}
        <div>
          <h3
            className="text-2xl font-bold text-white mb-8"
          >
            AI 开发工具
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {aiTools.map((tool, _index) => (
              <div
                key={tool.id}
                className={`bg-gray-800/50 backdrop-blur-sm border rounded-xl p-6 transition-all flex flex-col h-full ${
                  tool.special 
                    ? 'border-cyan-500/70 bg-gradient-to-br from-cyan-500/10 to-blue-500/10 hover:border-cyan-400' 
                    : 'border-gray-700 hover:border-cyan-500/50'
                }`}
              >
                <div className="flex justify-center mb-4 relative">
                  {tool.icon}
                  {tool.special ? (
                    <div className="absolute -top-2 -right-2 bg-gradient-to-r from-cyan-400 to-blue-500 text-black text-xs px-2 py-1 rounded-full font-bold">
                      推荐
                    </div>
                  ) : null}
                </div>
                <h4 className={`text-lg font-semibold text-center mb-2 ${
                  tool.special ? 'bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent' : 'text-white'
                }`}>{tool.name}</h4>
                <p className="text-gray-400 text-sm text-center mb-4 flex-grow">{tool.description}</p>
                
                <div className="text-center mb-4">
                  <span className="text-gray-400 text-sm">版本 {tool.version}</span>
                </div>

                <button
                  onClick={() => handleDownload(tool)}
                  disabled={!user}
                  className={`w-full inline-flex items-center justify-center gap-2 px-4 py-2 rounded-lg font-medium transition-all ${
                    !user 
                      ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      : tool.special 
                        ? 'bg-gradient-to-r from-cyan-400 to-blue-500 text-black shadow-lg shadow-cyan-500/25 hover:shadow-cyan-500/40' 
                        : 'bg-gradient-to-r from-cyan-400 to-blue-500 text-black hover:shadow-lg'
                  }`}
                >
                  <HiDownload />
                  {!user ? '需要登录' : '下载'}
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* 快速开始指南 */}
        <div
          className="mt-16 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-2xl p-8 border border-cyan-500/20"
        >
          <h3 className="text-2xl font-bold text-white mb-4">快速开始</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-4xl font-bold text-cyan-400 mb-2">1</div>
              <h4 className="text-lg font-semibold text-white mb-2">查看指南</h4>
              <p className="text-gray-400 text-sm">点击"下载"按钮，直接下载对应的安装文件</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-cyan-400 mb-2">2</div>
              <h4 className="text-lg font-semibold text-white mb-2">获取安装包</h4>
              <p className="text-gray-400 text-sm">联系系统管理员获取对应的插件安装包文件</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-cyan-400 mb-2">3</div>
              <h4 className="text-lg font-semibold text-white mb-2">配置使用</h4>
              <p className="text-gray-400 text-sm">按指南安装插件并配置平台API，开始AI编程</p>
            </div>
          </div>
        </div>

        {/* 下载说明 */}
        {/* <div
          className="mt-12 bg-gray-800/30 rounded-xl p-6 border border-gray-700"
        >
          <h4 className="text-lg font-semibold text-white mb-3">📋 下载说明</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-300">
            <div>
              <h5 className="font-medium text-cyan-400 mb-2">文件存放位置：</h5>
              <ul className="space-y-1">
                <li>• VS Code IDE: <code className="bg-gray-700 px-2 py-1 rounded text-xs">/downloads/tools/vscode-ide/</code></li>
                <li>• Cline插件: <code className="bg-gray-700 px-2 py-1 rounded text-xs">/downloads/tools/cline-plugin/</code></li>
                <li>• RooCode插件: <code className="bg-gray-700 px-2 py-1 rounded text-xs">/downloads/tools/roocode-plugin/</code></li>
                <li>• JetBrains AI: <code className="bg-gray-700 px-2 py-1 rounded text-xs">/downloads/tools/jetbrains-ai-assistant/</code></li>
              </ul>
            </div>
            <div>
              <h5 className="font-medium text-cyan-400 mb-2">安装提示：</h5>
              <ul className="space-y-1">
                <li>• 下载前请确保已登录账户</li>
                <li>• 每个工具目录包含详细的安装说明</li>
                <li>• 插件文件(.vsix)需要手动安装到IDE</li>
                <li>• 如有问题请查看对应的README文档</li>
              </ul>
            </div>
          </div>
        </div> */}
      </div>

             {/* 下载详情模态框 */}
       <>
         {showModal && selectedTool ? (
           <div
             className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
             onClick={() => setShowModal(false)}
           >
             <div
               className="bg-gray-800 rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
               onClick={(e) => e.stopPropagation()}
             >
               {/* 模态框头部 */}
               <div className="flex items-center justify-between p-6 border-b border-gray-700">
                 <div className="flex items-center gap-3">
                   {selectedTool.icon}
                   <h2 className="text-2xl font-bold text-white">{getDownloadInfo(selectedTool).title}</h2>
                 </div>
                 <button
                   onClick={() => setShowModal(false)}
                   className="text-gray-400 hover:text-white transition-colors"
                 >
                   <HiX className="text-2xl" />
                 </button>
               </div>

               {/* 下载链接 */}
               <div className="p-6">
                 <h3 className="text-lg font-semibold text-cyan-400 mb-4">📦 下载目录</h3>
                 <div className="mb-4 p-3 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                   <p className="text-blue-300 text-sm">
                     <span className="font-medium">💡 使用提示：</span>
                     点击"下载"按钮将直接下载对应的安装文件。
                     管理员会定期更新文件，确保您下载到最新版本。
                   </p>
                 </div>
                 <div className="space-y-3 mb-6">
                   {getDownloadInfo(selectedTool).downloadLinks.map((link, index) => (
                     <div key={index} className="p-4 bg-gray-700/50 rounded-lg border border-gray-600 hover:border-cyan-500/50 transition-colors">
                       <div className="flex items-start justify-between mb-2">
                         <div className="flex items-center gap-3">
                           <span className="text-2xl">{link.icon}</span>
                           <div>
                             <h5 className="text-white font-medium">{link.platform}</h5>
                             <p className="text-gray-400 text-sm">预计大小: {link.size}</p>
                           </div>
                         </div>
                                                <a
                         href={link.url}
                         download
                         className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-cyan-400 to-blue-500 text-black rounded-lg font-medium hover:shadow-lg transition-all"
                       >
                         <HiDownload />
                         下载
                       </a>
                       </div>
                       {link.description ? (
                         <p className="text-gray-300 text-sm mt-2 pl-11">{link.description}</p>
                       ) : null}
                     </div>
                   ))}
                 </div>

                 <h3 className="text-lg font-semibold text-cyan-400 mb-4">📋 安装步骤</h3>
                 <div className="space-y-3">
                   {getDownloadInfo(selectedTool).installSteps.map((step, index) => (
                     <div key={index} className="flex items-start gap-3">
                       <div className="flex-shrink-0 w-6 h-6 bg-cyan-500 text-black rounded-full flex items-center justify-center text-sm font-bold">
                         {index + 1}
                       </div>
                       <p className="text-gray-300 text-sm leading-relaxed">{step}</p>
                     </div>
                   ))}
                 </div>

                 {/* API配置信息 */}
                 {getDownloadInfo(selectedTool).config ? (
                   <div className="mt-6 p-4 bg-gray-700/50 rounded-lg border border-gray-600">
                     <h4 className="text-md font-semibold text-cyan-400 mb-3">⚙️ API配置信息</h4>
                     <div className="space-y-2">
                       <div className="flex items-center justify-between">
                         <span className="text-gray-400 text-sm">API地址:</span>
                         <div className="flex items-center gap-2">
                           <code className="bg-gray-800 px-2 py-1 rounded text-cyan-400 text-xs">
                             {getDownloadInfo(selectedTool).config.apiUrl}
                           </code>
                           <button
                             onClick={() => copyToClipboard(getDownloadInfo(selectedTool).config.apiUrl)}
                             className="text-gray-400 hover:text-cyan-400 transition-colors"
                           >
                             <FaCopy className="text-xs" />
                           </button>
                         </div>
                       </div>
                       <div className="flex items-center justify-between">
                         <span className="text-gray-400 text-sm">模型:</span>
                         <div className="flex items-center gap-2">
                           <code className="bg-gray-800 px-2 py-1 rounded text-cyan-400 text-xs">
                             {getDownloadInfo(selectedTool).config.model}
                           </code>
                           <button
                             onClick={() => copyToClipboard(getDownloadInfo(selectedTool).config.model)}
                             className="text-gray-400 hover:text-cyan-400 transition-colors"
                           >
                             <FaCopy className="text-xs" />
                           </button>
                         </div>
                       </div>
                       <p className="text-gray-400 text-xs mt-2">
                         💡 {getDownloadInfo(selectedTool).config.note}
                       </p>
                     </div>
                   </div>
                 ) : null}

                 {/* 注意事项 */}
                 {getDownloadInfo(selectedTool).notes && getDownloadInfo(selectedTool).notes.length > 0 ? (
                   <div className="mt-6">
                     <h4 className="text-md font-semibold text-yellow-400 mb-3">⚠️ 注意事项</h4>
                     <ul className="space-y-2">
                       {getDownloadInfo(selectedTool).notes.map((note, index) => (
                         <li key={index} className="text-gray-400 text-sm flex items-start gap-2">
                           <span className="text-yellow-400 mt-1">•</span>
                           <span>{note}</span>
                         </li>
                       ))}
                     </ul>
                   </div>
                 ) : null}

                 {/* 底部按钮 */}
                 <div className="mt-8 flex justify-center">
                   <button
                     onClick={() => setShowModal(false)}
                     className="px-6 py-2 bg-gradient-to-r from-cyan-400 to-blue-500 text-black rounded-lg font-medium hover:shadow-lg transition-all"
                   >
                     我知道了
                   </button>
                 </div>
               </div>
             </div>
           </div>
         ) : null}
       </>
    </section>
  );
};

export default DownloadsSection; 