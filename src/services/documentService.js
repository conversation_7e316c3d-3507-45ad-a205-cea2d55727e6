/**
 * 增强版文档服务 - 支持动态加载所有开发者文档内容
 * 提供智能搜索、上下文感知、缓存机制等功能
 */

class EnhancedDocumentService {
  constructor() {
    this.documentCache = new Map();
    this.configCache = null;
    this.lastUpdate = null;
    this.loading = false;
    this.allDocumentsLoaded = false;
    this.searchIndex = new Map();
    this.preloadPromise = null;

    // 新增：渐进式加载状态管理
    this.loadingProgress = {
      total: 0,
      loaded: 0,
      failed: 0,
      status: 'idle' // 'idle', 'loading', 'partial', 'complete', 'error'
    };

    // 新增：优先级加载队列
    this.priorityQueue = [];
    this.backgroundQueue = [];

    // 新增：加载超时控制
    this.loadTimeout = 30000; // 30秒超时
    this.retryAttempts = 3;

    // 新增：错误恢复机制
    this.errorRecovery = {
      failedDocuments: new Set(),
      retryCount: new Map(),
      lastError: null
    };
  }

  /**
   * 获取所有文档配置信息
   */
  async getDocumentConfig() {
    if (this.configCache && this.lastUpdate && Date.now() - this.lastUpdate < 300000) {
      return this.configCache;
    }

    try {
      const response = await fetch('/docs/config.json');
      const config = await response.json();
      this.configCache = config;
      this.lastUpdate = Date.now();
      return config;
    } catch (error) {
      console.error('获取文档配置失败:', error);
      return {
        categories: [],
        documents: {}
      };
    }
  }

  /**
   * 加载单个文档内容
   */
  async loadDocument(filePath) {
    const cacheKey = filePath;

    if (this.documentCache.has(cacheKey)) {
      console.log(`使用缓存的文档: ${filePath}`);
      return this.documentCache.get(cacheKey);
    }

    try {
      console.log(`正在加载文档: ${filePath}`);
      const response = await fetch(`/docs/${filePath}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const content = await response.text();

      if (!content.trim()) {
        throw new Error('文档内容为空');
      }

      // 解析Markdown内容，提取有用信息
      const parsedContent = this.parseMarkdownContent(content, filePath);

      this.documentCache.set(cacheKey, parsedContent);
      console.log(`文档加载成功: ${filePath} (${parsedContent.wordCount} 词)`);
      return parsedContent;
    } catch (error) {
      console.error(`加载文档失败: ${filePath}`, error);
      return null;
    }
  }

  /**
   * 带重试机制的文档加载（新增）
   */
  async loadDocumentWithRetry(filePath, attempt = 1) {
    try {
      // 添加超时控制
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('加载超时')), this.loadTimeout);
      });

      const loadPromise = this.loadDocument(filePath);
      const result = await Promise.race([loadPromise, timeoutPromise]);

      if (result) {
        // 重置重试计数
        this.errorRecovery.retryCount.delete(filePath);
        this.errorRecovery.failedDocuments.delete(filePath);
        return result;
      }

      throw new Error('文档加载返回空结果');
    } catch (error) {
      console.error(`文档加载失败 (尝试 ${attempt}/${this.retryAttempts}): ${filePath}`, error);

      if (attempt < this.retryAttempts) {
        // 记录重试次数
        this.errorRecovery.retryCount.set(filePath, attempt);

        // 指数退避重试
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
        await new Promise(resolve => setTimeout(resolve, delay));

        return this.loadDocumentWithRetry(filePath, attempt + 1);
      }

      // 重试失败，记录错误
      this.errorRecovery.failedDocuments.add(filePath);
      this.errorRecovery.retryCount.set(filePath, attempt);
      throw error;
    }
  }

  /**
   * 解析Markdown内容，提取结构化信息
   */
  parseMarkdownContent(content, filePath) {
    const lines = content.split('\n');
    const sections = [];
    let currentSection = null;
    let codeBlocks = [];
    let tables = [];
    let links = [];
    let images = [];
    let keyPoints = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // 提取标题
      if (line.startsWith('#')) {
        if (currentSection) {
          sections.push(currentSection);
        }
        currentSection = {
          level: line.match(/^#+/)[0].length,
          title: line.replace(/^#+\s*/, '').trim(),
          content: [],
          lineNumber: i + 1
        };
      } else if (currentSection) {
        currentSection.content.push(line);
      }

      // 提取代码块
      if (line.startsWith('```')) {
        const language = line.substring(3).trim();
        const codeContent = [];
        let j = i + 1;
        
        while (j < lines.length && !lines[j].startsWith('```')) {
          codeContent.push(lines[j]);
          j++;
        }
        
        if (j < lines.length) {
          codeBlocks.push({
            language: language || 'text',
            content: codeContent.join('\n'),
            lineStart: i + 1,
            lineEnd: j + 1
          });
          i = j;
        }
      }

      // 提取表格
      if (line.includes('|') && line.trim().length > 0) {
        const tableRow = line.split('|').map(cell => cell.trim()).filter(cell => cell);
        if (tableRow.length > 1) {
          tables.push({
            row: tableRow,
            lineNumber: i + 1
          });
        }
      }

      // 提取链接
      const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
      let linkMatch;
      while ((linkMatch = linkRegex.exec(line)) !== null) {
        links.push({
          text: linkMatch[1],
          url: linkMatch[2],
          lineNumber: i + 1
        });
      }

      // 提取图片
      const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g;
      let imageMatch;
      while ((imageMatch = imageRegex.exec(line)) !== null) {
        images.push({
          alt: imageMatch[1],
          src: imageMatch[2],
          lineNumber: i + 1
        });
      }

      // 提取关键点（以特殊符号开头的行）
      if (line.match(/^[*\-+]\s+/) || line.match(/^\d+\.\s+/) || line.match(/^>\s+/)) {
        keyPoints.push({
          text: line.replace(/^[*\-+\d.>\s]+/, '').trim(),
          type: line.match(/^[*\-+]\s+/) ? 'bullet' : 
                line.match(/^\d+\.\s+/) ? 'numbered' : 'quote',
          lineNumber: i + 1
        });
      }
    }

    if (currentSection) {
      sections.push(currentSection);
    }

    return {
      filePath,
      rawContent: content,
      sections,
      codeBlocks,
      tables,
      links,
      images,
      keyPoints,
      wordCount: content.split(/\s+/).length,
      lastParsed: new Date().toISOString()
    };
  }

  /**
   * 获取所有文档的结构化内容（优化版）
   */
  async getAllDocumentsContent() {
    if (this.allDocumentsLoaded && this.documentCache.size > 0) {
      console.log('使用缓存的文档数据');
      return this.organizeDocuments();
    }

    if (this.loading) {
      console.log('等待正在进行的加载...');
      return await this.preloadPromise;
    }

    console.log('开始渐进式加载文档...');
    this.loading = true;
    this.loadingProgress.status = 'loading';
    this.preloadPromise = this.progressiveLoadDocuments();

    try {
      const result = await this.preloadPromise;
      this.allDocumentsLoaded = true;
      this.loading = false;
      this.loadingProgress.status = 'complete';
      console.log('文档加载完成');
      return result;
    } catch (error) {
      console.error('加载文档失败:', error);
      this.loading = false;
      this.loadingProgress.status = 'error';
      this.errorRecovery.lastError = error;

      // 如果有部分文档加载成功，返回部分结果
      if (this.documentCache.size > 0) {
        console.log(`部分文档加载成功，返回 ${this.documentCache.size} 个文档`);
        this.loadingProgress.status = 'partial';
        return this.organizeDocuments();
      }

      return {};
    }
  }

  /**
   * 渐进式加载文档（新增）
   */
  async progressiveLoadDocuments() {
    const config = await this.getDocumentConfig();

    // 构建加载队列
    this.buildLoadingQueues(config);

    // 先加载优先级文档
    await this.loadPriorityDocuments();

    // 后台加载其余文档
    this.loadBackgroundDocuments();

    return this.organizeDocuments();
  }

  /**
   * 构建加载队列（新增）
   */
  buildLoadingQueues(config) {
    this.priorityQueue = [];
    this.backgroundQueue = [];

    for (const [categoryKey, documents] of Object.entries(config.documents)) {
      for (const doc of documents) {
        const item = { categoryKey, doc, filePath: doc.file };

        // 优先加载常用文档
        if (this.isPriorityDocument(doc)) {
          this.priorityQueue.push(item);
        } else {
          this.backgroundQueue.push(item);
        }
      }
    }

    this.loadingProgress.total = this.priorityQueue.length + this.backgroundQueue.length;
    console.log(`构建加载队列: 优先级 ${this.priorityQueue.length} 个, 后台 ${this.backgroundQueue.length} 个`);
  }

  /**
   * 判断是否为优先级文档（新增）
   */
  isPriorityDocument(doc) {
    const priorityKeywords = ['安装', '快速开始', '入门', 'getting-started', 'installation', 'setup'];
    const fileName = doc.file.toLowerCase();
    const title = (doc.title || '').toLowerCase();

    return priorityKeywords.some(keyword =>
      fileName.includes(keyword) || title.includes(keyword)
    );
  }

  /**
   * 加载优先级文档（新增）
   */
  async loadPriorityDocuments() {
    console.log('开始加载优先级文档...');
    const loadPromises = this.priorityQueue.map(item =>
      this.loadDocumentWithRetry(item.filePath)
        .then(content => {
          this.loadingProgress.loaded++;
          return { ...item, content };
        })
        .catch(error => {
          this.loadingProgress.failed++;
          this.errorRecovery.failedDocuments.add(item.filePath);
          console.error(`优先级文档加载失败: ${item.filePath}`, error);
          return { ...item, content: null, error };
        })
    );

    await Promise.allSettled(loadPromises);
    console.log(`优先级文档加载完成: ${this.loadingProgress.loaded}/${this.priorityQueue.length}`);
  }

  /**
   * 后台加载文档（新增）
   */
  loadBackgroundDocuments() {
    console.log('开始后台加载文档...');

    // 分批加载，避免阻塞
    const batchSize = 3;
    let currentBatch = 0;

    const loadNextBatch = async () => {
      const start = currentBatch * batchSize;
      const end = Math.min(start + batchSize, this.backgroundQueue.length);

      if (start >= this.backgroundQueue.length) return;

      const batch = this.backgroundQueue.slice(start, end);
      const batchPromises = batch.map(item =>
        this.loadDocumentWithRetry(item.filePath)
          .then(content => {
            this.loadingProgress.loaded++;
            return { ...item, content };
          })
          .catch(error => {
            this.loadingProgress.failed++;
            this.errorRecovery.failedDocuments.add(item.filePath);
            console.error(`后台文档加载失败: ${item.filePath}`, error);
            return { ...item, content: null, error };
          })
      );

      await Promise.allSettled(batchPromises);
      currentBatch++;

      // 继续下一批
      setTimeout(loadNextBatch, 100);
    };

    loadNextBatch();
  }

  /**
   * 加载所有文档
   */
  async loadAllDocuments() {
    const config = await this.getDocumentConfig();
    const loadPromises = [];

    // 并行加载所有文档
    for (const [categoryKey, documents] of Object.entries(config.documents)) {
      for (const doc of documents) {
        loadPromises.push(
          this.loadDocument(doc.file).then(content => ({
            categoryKey,
            doc,
            content
          }))
        );
      }
    }

    // 等待所有文档加载完成
    const results = await Promise.all(loadPromises);
    
    // 确保所有文档都已加载到缓存中
    console.log(`成功加载 ${results.length} 个文档`);
    
    // 验证文档是否真的加载到了缓存中
    console.log(`当前缓存中有 ${this.documentCache.size} 个文档`);
    
    return this.organizeDocuments();
  }

  /**
   * 组织文档数据
   */
  organizeDocuments() {
    const organized = {};

    for (const [filePath, content] of this.documentCache) {
      if (!content) continue;

      // 从文件路径推断分类
      const category = filePath.split('/')[0];

      if (!organized[category]) {
        organized[category] = [];
      }

      // 从配置中获取文档元数据
      const docMetadata = this.getDocumentMetadata(filePath);

      organized[category].push({
        filePath,
        content,
        id: docMetadata?.id || filePath.replace(/[^a-zA-Z0-9]/g, '-'),
        title: docMetadata?.title || content.sections[0]?.title || filePath,
        description: docMetadata?.description || this.generateDocumentSummary(content),
        tags: docMetadata?.tags || this.extractTags(content),
        readTime: docMetadata?.readTime || '5 分钟',
        summary: this.generateDocumentSummary(content)
      });
    }

    return organized;
  }

  /**
   * 从配置中获取文档元数据
   */
  getDocumentMetadata(filePath) {
    if (!this.configCache || !this.configCache.documents) return null;

    for (const [category, docs] of Object.entries(this.configCache.documents)) {
      const doc = docs.find(d => d.file === filePath);
      if (doc) return doc;
    }

    return null;
  }

  /**
   * 生成文档摘要
   */
  generateDocumentSummary(document) {
    const summary = {
      title: document.sections[0]?.title || '未命名文档',
      wordCount: document.wordCount,
      sectionsCount: document.sections.length,
      codeBlocksCount: document.codeBlocks.length,
      keyTopics: [],
      quickFacts: []
    };

    // 提取主要话题
    document.sections.forEach(section => {
      if (section.level <= 2) {
        summary.keyTopics.push(section.title);
      }
    });

    // 提取关键事实
    summary.quickFacts = document.keyPoints
      .filter(point => point.text.length > 20 && point.text.length < 200)
      .slice(0, 5)
      .map(point => point.text);

    return summary;
  }

  /**
   * 智能搜索文档（优化版）
   */
  async searchDocuments(query, options = {}) {
    const {
      category = null,
      searchType = 'comprehensive', // 'comprehensive', 'exact', 'fuzzy'
      maxResults = 10,
      includeContent = true,
      allowPartialResults = true // 新增：允许部分结果
    } = options;

    console.log(`开始搜索文档: "${query}"`);
    console.log(`搜索选项:`, { category, searchType, maxResults, allowPartialResults });

    // 检查文档加载状态
    const loadingStatus = this.getDetailedLoadingStatus();

    if (loadingStatus.canSearch) {
      console.log('文档库可用，直接搜索');
    } else if (loadingStatus.status === 'loading' && allowPartialResults && this.documentCache.size > 0) {
      console.log(`文档正在加载中，使用已加载的 ${this.documentCache.size} 个文档进行搜索`);
    } else if (loadingStatus.status === 'partial' && this.documentCache.size > 0) {
      console.log(`使用部分加载的文档进行搜索 (${this.documentCache.size} 个可用)`);
    } else if (!this.loading) {
      console.log('文档未加载，开始渐进式加载...');
      try {
        // 启动渐进式加载，但不等待全部完成
        this.getAllDocumentsContent();

        // 等待优先级文档加载完成
        await this.waitForPriorityDocuments();

        if (this.documentCache.size === 0) {
          throw new Error('无法加载任何文档');
        }
      } catch (error) {
        console.error('文档加载失败:', error);
        return {
          query,
          results: [],
          error: '文档库加载失败，请检查网络连接或稍后重试',
          loadingStatus: this.getDetailedLoadingStatus()
        };
      }
    } else {
      console.log('等待文档加载完成...');
      try {
        await this.waitForMinimumDocuments();
      } catch (error) {
        console.error('等待文档加载失败:', error);
        return {
          query,
          results: [],
          error: '文档库加载超时，请刷新页面重试',
          loadingStatus: this.getDetailedLoadingStatus()
        };
      }
    }

    const documents = await this.getAllDocumentsContent();
    console.log(`获取到的文档分类:`, Object.keys(documents));

    const totalDocs = Object.values(documents).reduce((sum, docs) => sum + docs.length, 0);
    console.log(`总共有 ${totalDocs} 个文档可供搜索`);

    // 如果没有文档，返回错误
    if (totalDocs === 0) {
      throw new Error('文档库为空，请检查文档配置');
    }

    const results = [];

    // 搜索所有文档
    for (const [categoryKey, categoryDocs] of Object.entries(documents)) {
      if (category && categoryKey !== category) continue;

      console.log(`搜索分类 "${categoryKey}" 中的 ${categoryDocs.length} 个文档`);

      for (const docData of categoryDocs) {
        const matches = this.searchInDocument(docData.content, query, searchType);

        if (matches.length > 0) {
          console.log(`在 ${docData.filePath} 中找到 ${matches.length} 个匹配`);
          results.push({
            category: categoryKey,
            filePath: docData.filePath,
            summary: docData.summary,
            document: {
              title: docData.title || docData.filePath,
              description: docData.description || '',
              tags: docData.tags || []
            },
            matches,
            relevanceScore: this.calculateRelevanceScore(matches, query),
            content: includeContent ? docData.content : null
          });
        }
      }
    }

    // 按相关性排序
    results.sort((a, b) => b.relevanceScore - a.relevanceScore);

    console.log(`搜索完成，找到 ${results.length} 个匹配结果`);
    return results.slice(0, maxResults);
  }

  /**
   * 在单个文档中搜索
   */
  searchInDocument(document, query, searchType) {
    const matches = [];
    const queryLower = query.toLowerCase();

    // 搜索标题
    document.sections.forEach(section => {
      const titleLower = section.title.toLowerCase();
      if (titleLower.includes(queryLower)) {
        matches.push({
          type: 'title',
          content: section.title,
          context: section.content.slice(0, 3).join(' ').substring(0, 200),
          relevance: searchType === 'exact' ? 1.0 : 0.9
        });
      }
    });

    // 搜索内容
    document.sections.forEach(section => {
      const fullContent = section.content.join(' ');
      const contentLower = fullContent.toLowerCase();
      
      if (contentLower.includes(queryLower)) {
        matches.push({
          type: 'content',
          content: section.title,
          context: this.extractMatchContext(fullContent, query),
          relevance: 0.7
        });
      }
    });

    // 搜索代码块
    document.codeBlocks.forEach(block => {
      const codeLower = block.content.toLowerCase();
      if (codeLower.includes(queryLower)) {
        matches.push({
          type: 'code',
          content: `${block.language} 代码`,
          context: block.content.substring(0, 200),
          relevance: 0.8
        });
      }
    });

    // 搜索关键点
    document.keyPoints.forEach(point => {
      const pointLower = point.text.toLowerCase();
      if (pointLower.includes(queryLower)) {
        matches.push({
          type: 'keypoint',
          content: point.text,
          context: point.text,
          relevance: 0.6
        });
      }
    });

    return matches;
  }

  /**
   * 提取匹配上下文
   */
  extractMatchContext(content, query, contextLength = 200) {
    const queryIndex = content.toLowerCase().indexOf(query.toLowerCase());
    if (queryIndex === -1) return content.substring(0, contextLength);

    const start = Math.max(0, queryIndex - contextLength / 2);
    const end = Math.min(content.length, queryIndex + query.length + contextLength / 2);
    
    return content.substring(start, end);
  }

  /**
   * 计算相关性得分
   */
  calculateRelevanceScore(matches, _query) {
    if (matches.length === 0) return 0;

    let totalScore = 0;
    let titleBonus = 0;
    
    matches.forEach(match => {
      totalScore += match.relevance;
      if (match.type === 'title') titleBonus += 0.3;
    });

    // 平均相关性 + 匹配数量权重 + 标题匹配奖励
    return totalScore / matches.length + Math.log(matches.length + 1) * 0.1 + titleBonus;
  }

  /**
   * 获取文档统计信息
   */
  async getDocumentStats() {
    const documents = await this.getAllDocumentsContent();
    const stats = {
      totalDocuments: 0,
      totalWords: 0,
      totalSections: 0,
      totalCodeBlocks: 0,
      categoriesCount: Object.keys(documents).length,
      categories: {},
      isLoaded: this.allDocumentsLoaded,
      isLoading: this.loading,
      loadingStatus: this.getLoadingStatus(),
      canSearch: this.isReadyForSearch()
    };

    for (const [category, docs] of Object.entries(documents)) {
      stats.categories[category] = {
        documentCount: docs.length,
        totalWords: 0,
        totalSections: 0,
        totalCodeBlocks: 0
      };

      docs.forEach(doc => {
        stats.totalDocuments++;
        stats.totalWords += doc.content.wordCount;
        stats.totalSections += doc.content.sections.length;
        stats.totalCodeBlocks += doc.content.codeBlocks.length;

        stats.categories[category].totalWords += doc.content.wordCount;
        stats.categories[category].totalSections += doc.content.sections.length;
        stats.categories[category].totalCodeBlocks += doc.content.codeBlocks.length;
      });
    }

    return stats;
  }

  /**
   * 获取加载状态详情
   */
  getLoadingStatus() {
    if (this.allDocumentsLoaded) {
      return {
        status: 'loaded',
        message: '文档库已完全加载',
        canSearch: true
      };
    }

    if (this.loading) {
      return {
        status: 'loading',
        message: '文档库正在加载中...',
        canSearch: false
      };
    }

    return {
      status: 'not_loaded',
      message: '文档库未加载',
      canSearch: false
    };
  }

  /**
   * 获取详细加载状态（新增）
   */
  getDetailedLoadingStatus() {
    const basicStatus = this.getLoadingStatus();

    return {
      ...basicStatus,
      progress: this.loadingProgress,
      cacheSize: this.documentCache.size,
      errorInfo: {
        failedDocuments: Array.from(this.errorRecovery.failedDocuments),
        retryCount: Object.fromEntries(this.errorRecovery.retryCount),
        lastError: this.errorRecovery.lastError?.message || null
      },
      canPartialSearch: this.documentCache.size > 0,
      recommendations: this.generateLoadingRecommendations()
    };
  }

  /**
   * 等待优先级文档加载完成（新增）
   */
  async waitForPriorityDocuments(timeout = 10000) {
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      if (this.priorityQueue.length === 0 ||
          this.loadingProgress.loaded >= this.priorityQueue.length) {
        return;
      }

      await new Promise(resolve => setTimeout(resolve, 100));
    }

    throw new Error('等待优先级文档加载超时');
  }

  /**
   * 等待最少数量的文档加载完成（新增）
   */
  async waitForMinimumDocuments(minCount = 3, timeout = 15000) {
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      if (this.documentCache.size >= minCount) {
        return;
      }

      await new Promise(resolve => setTimeout(resolve, 200));
    }

    if (this.documentCache.size === 0) {
      throw new Error('无法加载任何文档');
    }

    console.log(`加载超时，但已有 ${this.documentCache.size} 个文档可用`);
  }

  /**
   * 生成加载建议（新增）
   */
  generateLoadingRecommendations() {
    const recommendations = [];

    if (this.errorRecovery.failedDocuments.size > 0) {
      recommendations.push(`${this.errorRecovery.failedDocuments.size} 个文档加载失败，可能影响搜索结果`);
    }

    if (this.loadingProgress.status === 'loading' && this.documentCache.size > 0) {
      recommendations.push('文档正在后台加载中，当前可搜索部分内容');
    }

    if (this.loadingProgress.status === 'error' && this.documentCache.size === 0) {
      recommendations.push('建议检查网络连接或刷新页面重试');
    }

    if (this.loadingProgress.failed > this.loadingProgress.loaded / 2) {
      recommendations.push('大量文档加载失败，建议检查文档服务状态');
    }

    return recommendations;
  }

  /**
   * 检查文档库是否可用于搜索
   */
  isReadyForSearch() {
    return this.allDocumentsLoaded && this.documentCache.size > 0;
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.documentCache.clear();
    this.configCache = null;
    this.lastUpdate = null;
    this.allDocumentsLoaded = false;
    this.searchIndex.clear();
  }

  /**
   * 预加载所有文档（用于页面初始化）
   */
  async preloadAllDocuments() {
    if (this.allDocumentsLoaded) return;
    
    console.log('开始预加载所有文档...');
    const startTime = Date.now();
    
    await this.getAllDocumentsContent();
    
    const endTime = Date.now();
    console.log(`文档预加载完成，耗时: ${endTime - startTime}ms`);
    
    return this.getDocumentStats();
  }

  /**
   * 获取特定类别的文档
   */
  async getDocumentsByCategory(category) {
    const allDocs = await this.getAllDocumentsContent();
    return allDocs[category] || [];
  }

  /**
   * 获取文档的具体内容用于AI回答
   */
  async getDocumentContentForAI(filePath) {
    const document = await this.loadDocument(filePath);
    if (!document) return null;

    // 为AI优化的文档结构
    return {
      title: document.sections[0]?.title || '文档',
      summary: this.generateDocumentSummary(document),
      mainSections: document.sections.filter(s => s.level <= 2).map(s => ({
        title: s.title,
        content: s.content.join(' ').substring(0, 500)
      })),
      keyPoints: document.keyPoints.slice(0, 10),
      codeExamples: document.codeBlocks.slice(0, 3).map(block => ({
        language: block.language,
        content: block.content.substring(0, 300)
      })),
      quickAnswers: this.generateQuickAnswers(document)
    };
  }

  /**
   * 生成快速回答
   */
  generateQuickAnswers(document) {
    const answers = [];
    
    // 从标题生成问答
    document.sections.forEach(section => {
      if (section.title.includes('如何') || section.title.includes('怎么')) {
        answers.push({
          question: section.title,
          answer: section.content.slice(0, 3).join(' ').substring(0, 200)
        });
      }
    });

    return answers.slice(0, 5);
  }
}

// 创建全局实例
const enhancedDocumentService = new EnhancedDocumentService();

export default enhancedDocumentService;
export { EnhancedDocumentService }; 