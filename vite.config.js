import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import legacy from '@vitejs/plugin-legacy'
import { visualizer } from 'rollup-plugin-visualizer';

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    legacy({
      // 指定要支持的传统浏览器
      targets: [
        '> 0.5%',
        'last 2 versions',
        'Firefox ESR',
        'not dead',
        'not IE 11',
        'Chrome >= 60',
        'Firefox >= 60',
        'Safari >= 11',
        'Edge >= 79'
      ],
      // 现代浏览器的 polyfills
      modernPolyfills: true,
      // 使用 core-js 提供更好的 polyfill 支持
      additionalLegacyPolyfills: ['regenerator-runtime/runtime']
    }),
    visualizer({ open: false, filename: 'logs/stats.html' })
  ],
  server: {
    host: '0.0.0.0',
    port: 5173,
    strictPort: false,
    open: false
  },
  build: {
    // 优化构建性能和兼容性
    cssTarget: 'chrome61',
    minify: 'terser', // 更好的压缩
    cssMinify: 'esbuild', // CSS 使用 esbuild 压缩
    modulePreload: {
      polyfill: true
    },
    sourcemap: false, // 生产环境关闭 sourcemap
    
    rollupOptions: {
      output: {
        // 更激进的代码分割策略
        manualChunks: (id) => {
          if (id.includes('node_modules')) {
            if (id.includes('react-dom')) return 'vendor-react-dom';
            if (id.includes('react-router')) return 'vendor-router';
            if (id.includes('react-icons')) return 'vendor-icons';
            if (id.includes('react')) return 'vendor-react';
            return 'vendor';
          }
          if (id.includes('src/components')) {
            if (id.includes('AIAssistantSection')) return 'component-ai-assistant';
            if (id.includes('DocumentationSection')) return 'component-docs';
            if (id.includes('DownloadsSection')) return 'component-downloads';
    
            return 'components';
          }
        },
        
        // 文件命名优化
        chunkFileNames: () => {
          return `assets/[name]-[hash].js`;
        },
        
        assetFileNames: (assetInfo) => {
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name)) {
            return `assets/images/[name]-[hash][extname]`;
          }
          if (/\.(css)$/i.test(assetInfo.name)) {
            return `assets/css/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        }
      }
    },
    
    // 提升chunk大小警告阈值，但不过度
    chunkSizeWarningLimit: 700,
    
    // 压缩选项 - 优化更多
    terserOptions: {
      compress: {
        pure_funcs: ['console.log', 'console.info', 'console.warn'], // 移除更多console
        passes: 3, // 增加压缩次数
        unsafe: true,
        unsafe_comps: true,
        unsafe_Function: true,
        unsafe_math: true,
        unsafe_symbols: true,
        unsafe_methods: true,
        unsafe_proto: true,
        unsafe_regexp: true,
        unsafe_undefined: true,
        arguments: true,
        booleans_as_integers: true,
        collapse_vars: true,
        comparisons: true,
        computed_props: true,
        conditionals: true,
        dead_code: true,
        directives: true,
        drop_console: true,
        drop_debugger: true,
        ecma: 2020,
        evaluate: true,
        expression: false,
        global_defs: {},
        hoist_funs: false,
        hoist_props: true,
        hoist_vars: false,
        if_return: true,
        inline: 3,
        join_vars: true,
        keep_fargs: true,
        keep_fnames: false,
        keep_infinity: false,
        loops: true,
        negate_iife: true,
        properties: true,
        reduce_funcs: true,
        reduce_vars: true,
        sequences: true,
        side_effects: true,
        switches: true,
        top_retain: null,
        typeofs: true,
        unused: true,
        warnings: false
      },
      mangle: {
        safari10: true,
        toplevel: true
      },
      format: {
        comments: false
      }
    },
    
    // 资源内联阈值
    assetsInlineLimit: 4096
  },
  
  // 优化依赖预构建
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-icons/fa',
      'react-icons/hi',
      'lucide-react',
      'axios'
    ],
    exclude: [
      'openai', // 后端专用
      '@anthropic-ai/sdk', // 后端专用
      'express', // 后端专用
      'cors', // 后端专用
      'ldapjs', // 后端专用
      'node-fetch', // 后端专用
      'dotenv', // 后端专用
      'uuid', // 按需导入
      'concurrently', // 开发工具
      'react-router-dom' // 当前未使用
    ],
    force: false,
    esbuildOptions: {
      target: 'es2020',
      // 移除未使用的代码
      treeShaking: true,
      // 压缩标识符
      minifyIdentifiers: true,
      // 压缩语法
      minifySyntax: true,
      // 压缩空格
      minifyWhitespace: true
    }
  },
  
  // CSS优化
  css: {
    devSourcemap: false,
    preprocessorOptions: {
      scss: {
        charset: false
      }
    }
  },

  // 静态资源处理 - 支持现代图片格式
  assetsInclude: ['**/*.webp', '**/*.avif', '**/*.jpg', '**/*.jpeg', '**/*.png'],
  
  // 性能优化
  esbuild: {
    drop: ['console', 'debugger'],
    legalComments: 'none'
  }
})
