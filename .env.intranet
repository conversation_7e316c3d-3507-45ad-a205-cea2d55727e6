# 内网环境配置文件
# 复制此文件为 .env 并根据实际情况修改配置

# 运行环境
NODE_ENV=production

# 前端访问地址（替换为实际的内网地址）
PROD_FRONTEND_URL=http://your-intranet-domain.com

# 内网部署标识
DISABLE_EXTERNAL_LINKS=true

# 如果有内网浏览器下载服务器，配置此地址
BROWSER_DOWNLOAD_BASE_URL=

# 服务配置（根据实际情况修改）
LDAP_SERVER=your-ldap-server
LDAP_PORT=389
LDAP_BIND_DN=cn=admin,dc=company,dc=com
LDAP_SEARCH_BASE=ou=users,dc=company,dc=com

# NLWeb API 配置
NLWEB_API_URL=http://your-nlweb-api:port
NLWEB_API_KEY=your-api-key

# LiteLLM API 配置  
LITELLM_API_URL=http://your-litellm-api:port
LITELLM_API_KEY=your-api-key

# 其他内网服务配置
# 根据实际部署的服务进行配置
