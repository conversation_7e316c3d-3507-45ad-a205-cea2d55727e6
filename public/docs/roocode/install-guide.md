# RooCode插件安装配置指南

> RooCode多模式AI助手的详细安装配置教程，支持VS Code、Cursor等编辑器

## 🔧 系统要求

- VS Code 1.84.0 或更高版本
- 支持Cursor、Windsurf等VS Code兼容编辑器
- 稳定的网络连接（内网环境）
- 平台API密钥（在API密钥管理页面获取）
- 操作系统：Windows、macOS、Linux

![系统要求检查](./images/roocode-system-requirements.png)

## 📦 获取安装包

1. 访问平台"工具下载中心"页面
2. 确保已登录平台账户
3. 点击"RooCode插件"的"安装指南"按钮
4. 在本页面上方下载.vsix安装包文件
5. 将安装包保存到本地目录

## 🚀 安装步骤

### VS Code 安装
1. 打开VS Code编辑器
2. 按 `Ctrl+Shift+P` (Mac: `Cmd+Shift+P`) 打开命令面板
3. 输入 "Extensions: Install from VSIX" 并选择
4. 浏览并选择下载的RooCode插件.vsix文件
5. 等待安装完成，编辑器会显示安装成功提示
6. 重启编辑器使插件生效

### Cursor 安装
1. 打开Cursor编辑器
2. 按 `Ctrl+Shift+P` (Mac: `Cmd+Shift+P`) 打开命令面板
3. 输入 "Extensions: Install from VSIX" 并选择
4. 选择RooCode插件.vsix文件
5. 重启Cursor

![安装过程示例](./images/roocode-installation-process.png)

## ⚙️ API配置

### 基础配置步骤
1. 点击RooCode扩展面板的齿轮图标
2. 选择"Providers"进行API配置
3. API Provider：选择 "OpenAI Compatible"
4. API基础URL：`http://192.168.60.52:14000` (对于部分插件，可尝试`http://192.168.60.52:14000/v1`)
5. 模型名称：`deepseek-reasoner`
6. API密钥：粘贴您在平台生成的密钥
7. 保存配置并测试连接

### 配置文件示例
```json
{
  "roocode.providers": [
    {
      "id": "custom-openai",
      "name": "内网AI平台",
      "type": "openai-compatible",
      "baseUrl": "http://192.168.60.52:14000",
      "model": "deepseek-reasoner",
      "apiKey": "your-api-key-here",
      "maxTokens": 4000
    }
  ],
  "roocode.defaultProvider": "custom-openai"
}
```

### 高级配置选项
```json
{
  "roocode.conversation": {
    "maxMessages": 50,
    "maxTokensPerMessage": 1000
  },
  "roocode.codeGeneration": {
    "autoFormat": true,
    "includeComments": true,
    "followProjectStyle": true
  },
  "roocode.ui": {
    "theme": "dark",
    "fontSize": 14,
    "showLineNumbers": true
  }
}
```

## 💡 首次使用

### 启动RooCode
1. 在编辑器中打开您的项目文件夹
2. 点击左侧活动栏的RooCode图标（袋鼠图标）
3. 选择合适的工作模式（Code/Architect/Ask/Debug）
4. 用自然语言描述您的需求
5. 预览AI建议的操作和代码更改
6. 批准或拒绝每个建议的操作

### 工作模式快速选择
RooCode提供四种工作模式，可在聊天输入框左下角的下拉菜单中切换：

- **Code模式** 🔨 - 通用编程任务和代码生成
- **Architect模式** 🏗️ - 系统架构设计和技术规划
- **Ask模式** ❓ - 技术咨询和知识问答
- **Debug模式** 🐛 - 系统化问题诊断和调试

![RooCode界面示例](./images/roocode-interface-overview.png)

## 📹 安装演示视频

<video controls width="100%" style="max-width: 800px;">
  <source src="./videos/roocode-installation-demo.mp4" type="video/mp4">
  您的浏览器不支持视频播放。
</video>

## 🔧 常见问题解决

### Q: 插件安装后看不到袋鼠图标？
**A**: 请尝试以下解决方案：
1. 重启编辑器
2. 检查扩展是否正确启用：`Ctrl+Shift+X` → 搜索"RooCode" → 确保已启用
3. 查看是否有错误提示：`View` → `Output` → 选择"RooCode"

### Q: API连接测试失败？
**A**: 请检查以下配置：
- API地址是否正确：`http://192.168.60.52:14000`
- API密钥是否有效且未过期
- 网络连接是否正常
- 防火墙是否阻止了连接

### Q: 如何在多个编辑器间同步配置？
**A**: RooCode支持配置导入导出：
1. 在已配置的编辑器中：Settings → Export Configuration
2. 在新编辑器中：Settings → Import Configuration
3. 选择导出的配置文件

### Q: 支持哪些编辑器？
**A**: RooCode支持所有基于VS Code的编辑器：
- VS Code (稳定版、内测版)
- Cursor
- Windsurf
- Zed (部分功能)
- 其他VS Code兼容编辑器

## 🚀 性能优化建议

### 减少内存占用
```json
{
  "roocode.performance": {
    "maxCacheSize": "100MB",
    "enableLazyLoading": true,
    "compressHistory": true
  }
}
```

### 网络优化
```json
{
  "roocode.network": {
    "timeout": 30000,
    "retryAttempts": 3,
    "enableCompression": true
  }
}
```

## 📊 使用统计

查看RooCode使用情况：
1. 点击RooCode面板右上角的统计图标
2. 查看API调用次数、成功率等指标
3. 监控配额使用情况

---

*最后更新时间：2024年12月* 