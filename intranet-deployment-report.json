{"timestamp": "2025-06-19T14:15:11.375Z", "summary": {"ready": false, "externalLinksCount": 1, "localResourcesCount": 7}, "externalLinks": [{"file": "dist/*", "matches": "./dist/assets/fonts/fontawesome.css.backup\n./dist/assets/react-vendor-legacy-BWPihPEV.js\n./dist/assets/polyfills-CQfVnDiW.js\n./dist/assets/vendor-B7-9gH-V.js\n./dist/assets/webfonts/fa-regular-400.ttf\n./dist/assets/webfonts/fa-brands-400.ttf\n./dist/assets/webfonts/fa-solid-900.ttf\n./dist/assets/polyfills-legacy-fB4mKvjL.js\n./dist/assets/react-vendor-BJRxOCaC.js\n./dist/assets/vendor-legacy-C-WgpDiM.js\n./dist/downloads/tools/jetbrains-ai-assistant/latest/ml-llm-252.21735.40.zip"}], "localResources": [{"type": "字体", "path": "public/assets/fonts", "count": 3}, {"type": "字体", "path": "public/assets/webfonts", "count": 6}, {"type": "静态资源", "path": "public/assets", "count": 9}, {"type": "静态资源", "path": "src/assets", "count": 1}, {"type": "npm依赖", "path": "package.json", "count": 30}, {"type": "Service Worker", "path": "public/sw.js", "count": 1}, {"type": "PWA Manifest", "path": "public/manifest.json", "count": 1}], "recommendations": ["运行内网优化脚本: node scripts/prepare-intranet-deployment.cjs"]}